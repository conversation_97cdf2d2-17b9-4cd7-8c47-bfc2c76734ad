# Copyright 2020 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from . import (
    albert,
    align,
    altclip,
    audio_spectrogram_transformer,
    auto,
    autoformer,
    bark,
    bart,
    barthez,
    bartpho,
    beit,
    bert,
    bert_generation,
    bert_japanese,
    bertweet,
    big_bird,
    bigbird_pegasus,
    biogpt,
    bit,
    blenderbot,
    blenderbot_small,
    blip,
    blip_2,
    bloom,
    bridgetower,
    bros,
    byt5,
    camembert,
    canine,
    chinese_clip,
    clap,
    clip,
    clipseg,
    code_llama,
    codegen,
    conditional_detr,
    convbert,
    convnext,
    convnextv2,
    cpm,
    cpmant,
    ctrl,
    cvt,
    data2vec,
    deberta,
    deberta_v2,
    decision_transformer,
    deformable_detr,
    deit,
    deprecated,
    deta,
    detr,
    dialogpt,
    dinat,
    dinov2,
    distilbert,
    dit,
    donut,
    dpr,
    dpt,
    efficientformer,
    efficientnet,
    electra,
    encodec,
    encoder_decoder,
    ernie,
    ernie_m,
    esm,
    falcon,
    flaubert,
    flava,
    fnet,
    focalnet,
    fsmt,
    funnel,
    fuyu,
    git,
    glpn,
    gpt2,
    gpt_bigcode,
    gpt_neo,
    gpt_neox,
    gpt_neox_japanese,
    gpt_sw3,
    gptj,
    gptsan_japanese,
    graphormer,
    groupvit,
    herbert,
    hubert,
    ibert,
    idefics,
    imagegpt,
    informer,
    instructblip,
    jukebox,
    kosmos2,
    layoutlm,
    layoutlmv2,
    layoutlmv3,
    layoutxlm,
    led,
    levit,
    lilt,
    llama,
    longformer,
    longt5,
    luke,
    lxmert,
    m2m_100,
    marian,
    markuplm,
    mask2former,
    maskformer,
    mbart,
    mbart50,
    mega,
    megatron_bert,
    megatron_gpt2,
    mgp_str,
    mistral,
    mluke,
    mobilebert,
    mobilenet_v1,
    mobilenet_v2,
    mobilevit,
    mobilevitv2,
    mpnet,
    mpt,
    mra,
    mt5,
    musicgen,
    mvp,
    nat,
    nezha,
    nllb,
    nllb_moe,
    nougat,
    nystromformer,
    oneformer,
    openai,
    opt,
    owlv2,
    owlvit,
    pegasus,
    pegasus_x,
    perceiver,
    persimmon,
    phobert,
    pix2struct,
    plbart,
    poolformer,
    pop2piano,
    prophetnet,
    pvt,
    qdqbert,
    rag,
    realm,
    reformer,
    regnet,
    rembert,
    resnet,
    roberta,
    roberta_prelayernorm,
    roc_bert,
    roformer,
    rwkv,
    sam,
    seamless_m4t,
    segformer,
    sew,
    sew_d,
    speech_encoder_decoder,
    speech_to_text,
    speech_to_text_2,
    speecht5,
    splinter,
    squeezebert,
    swiftformer,
    swin,
    swin2sr,
    swinv2,
    switch_transformers,
    t5,
    table_transformer,
    tapas,
    time_series_transformer,
    timesformer,
    timm_backbone,
    transfo_xl,
    trocr,
    tvlt,
    umt5,
    unispeech,
    unispeech_sat,
    upernet,
    videomae,
    vilt,
    vision_encoder_decoder,
    vision_text_dual_encoder,
    visual_bert,
    vit,
    vit_hybrid,
    vit_mae,
    vit_msn,
    vitdet,
    vitmatte,
    vits,
    vivit,
    wav2vec2,
    wav2vec2_conformer,
    wav2vec2_phoneme,
    wav2vec2_with_lm,
    wavlm,
    whisper,
    x_clip,
    xglm,
    xlm,
    xlm_prophetnet,
    xlm_roberta,
    xlm_roberta_xl,
    xlnet,
    xmod,
    yolos,
    yoso,
)
